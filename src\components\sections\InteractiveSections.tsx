import React from "react";
import Container from "../Container";

// Best Sellers Section Component
const items = [
  { name: "Produk A", desc: "Deskripsi singkat produk A", price: "Rp 149.000" },
  { name: "Produk B", desc: "Deskripsi singkat produk B", price: "Rp 199.000" },
  { name: "Produk C", desc: "Deskripsi singkat produk C", price: "Rp 249.000" },
];

export function BestSellers() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Best Sellers</h2>
        <div className="mt-6 grid gap-6 sm:grid-cols-3">
          {items.map((p) => (
            <div key={p.name} className="rounded-xl border p-6">
              <div className="aspect-video rounded-lg bg-zinc-100 dark:bg-zinc-800" />
              <h3 className="mt-4 font-heading font-semibold">{p.name}</h3>
              <p className="text-sm text-muted-foreground">{p.desc}</p>
              <div className="mt-3 font-medium">{p.price}</div>
              <button className="mt-4 inline-flex h-10 items-center justify-center rounded-md bg-black text-white px-4 text-sm hover:opacity-90">
                Beli
              </button>
            </div>
          ))}
        </div>
      </Container>
    </section>
  );
}

// Testimonials Section Component
const testimonials = [
  { name: "Andi", role: "Pengusaha", quote: "Template ini bikin landing page saya siap jual dalam hitungan jam." },
  { name: "Sari", role: "Marketing", quote: "Desain clean dan mudah diubah sesuai brand." },
  { name: "Budi", role: "Founder", quote: "Performa cepat dan SEO oke. Recommended!" },
];

export function Testimonials() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Testimoni</h2>
        <div className="mt-6 grid gap-6 sm:grid-cols-3">
          {testimonials.map((t) => (
            <figure key={t.name} className="rounded-xl border p-6 bg-white/50 dark:bg-zinc-900/50">
              <blockquote className="text-sm text-foreground">"{t.quote}"</blockquote>
              <figcaption className="mt-4 text-sm font-medium">
                {t.name} · <span className="text-muted-foreground-light">{t.role}</span>
              </figcaption>
            </figure>
          ))}
        </div>
      </Container>
    </section>
  );
}

// Gallery Section Component
export function Gallery() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">Galeri</h2>
        <div className="mt-6 grid grid-cols-2 sm:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="aspect-square rounded-lg bg-zinc-100 dark:bg-zinc-800" />
          ))}
        </div>
      </Container>
    </section>
  );
}

// FAQ Section Component
const faqs = [
  { q: "Apakah bisa di-deploy ke Vercel?", a: "Ya, Next.js sangat cocok untuk Vercel." },
  { q: "Apakah mudah diubah?", a: "Semua komponen dibuat modular dan mudah dikustomisasi." },
  { q: "Apakah SEO-friendly?", a: "Iya, dengan meta tag dan praktik Next.js modern." },
];

export function FAQ() {
  return (
    <section className="py-16 sm:py-20">
      <Container>
        <h2 className="text-2xl sm:text-3xl font-heading font-semibold">FAQ</h2>
        <div className="mt-6 grid gap-4">
          {faqs.map((f) => (
            <details key={f.q} className="rounded-lg border p-4">
              <summary className="cursor-pointer font-medium">{f.q}</summary>
              <p className="mt-2 text-sm text-muted-foreground">{f.a}</p>
            </details>
          ))}
        </div>
      </Container>
    </section>
  );
}
