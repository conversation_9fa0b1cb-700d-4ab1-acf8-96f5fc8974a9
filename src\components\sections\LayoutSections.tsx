import React from "react";
import Container from "../Container";
import ThemeToggle from "../ThemeToggle";

// Header Navigation Component
export function HeaderNav() {
  return (
    <header className="sticky top-0 z-50 backdrop-blur supports-[backdrop-filter]:bg-white/60 bg-white/80 dark:bg-zinc-950/60 border-b">
      <Container className="h-14 flex items-center justify-between">
        <div className="font-heading font-semibold">Brand</div>
        <nav className="hidden sm:flex items-center gap-6 text-sm">
          <a href="#benefits" className="hover:underline">Benefits</a>
          <a href="#location" className="hover:underline">Lokasi</a>
          <a href="#bestsellers" className="hover:underline">Best Sellers</a>
          <a href="#testimonials" className="hover:underline">Testimoni</a>
          <a href="#about" className="hover:underline">Tentang <PERSON></a>
          <a href="#gallery" className="hover:underline"><PERSON><PERSON></a>
          <a href="#faq" className="hover:underline">FAQ</a>
        </nav>
        <div className="flex items-center gap-3">
          <ThemeToggle />
          <a className="inline-flex h-9 items-center justify-center rounded-md bg-black dark:bg-white text-white dark:text-black px-4 text-sm hover:opacity-90 transition-opacity">
            Contact
          </a>
        </div>
      </Container>
    </header>
  );
}

// Site Footer Component
export function SiteFooter() {
  return (
    <footer className="border-t py-10 mt-10">
      <Container className="flex flex-col sm:flex-row items-center justify-between gap-4">
        <div className="text-sm text-muted-foreground-light">© {new Date().getFullYear()} Brand. All rights reserved.</div>
        <nav className="flex items-center gap-4 text-sm">
          <a className="hover:underline" href="#">Privacy</a>
          <a className="hover:underline" href="#">Terms</a>
          <a className="hover:underline" href="#">Contact</a>
        </nav>
      </Container>
    </footer>
  );
}
